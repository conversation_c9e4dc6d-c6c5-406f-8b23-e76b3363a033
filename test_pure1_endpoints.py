#!/usr/bin/env python3
"""
Test script to explore Pure1 API endpoints and check connectivity.
"""

import requests
import json
from dotenv import load_dotenv
import os


def load_config():
    """Load configuration from .env file."""
    if os.path.exists('.env'):
        load_dotenv('.env')
    else:
        load_dotenv('.env.example')
    
    return {
        'api_base_url': os.getenv('PURESTORAGE__API_BASE_URL', 'https://api.pure1.purestorage.com'),
        'proxy_url': os.getenv('PURESTORAGE__PROXY_URL')
    }


def test_endpoint(session, url, description):
    """Test an endpoint and return results."""
    print(f"\nTesting: {description}")
    print(f"URL: {url}")
    
    try:
        response = session.get(url, timeout=30)
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"Response: {json.dumps(data, indent=2)}")
            except:
                print(f"Response (text): {response.text}")
        else:
            print(f"Response: {response.text}")
            
        return response
    except Exception as e:
        print(f"Error: {e}")
        return None


def main():
    """Main test function."""
    print("Pure1 API Endpoint Testing")
    print("=" * 50)
    
    config = load_config()
    print(f"API Base URL: {config['api_base_url']}")
    print(f"Proxy URL: {config['proxy_url'] or 'None'}")
    
    # Configure session
    session = requests.Session()
    if config['proxy_url']:
        session.proxies = {
            'http': config['proxy_url'],
            'https': config['proxy_url']
        }
    
    base_url = config['api_base_url']
    
    # Test various endpoints
    endpoints = [
        (f"{base_url}", "Base URL"),
        (f"{base_url}/", "Base URL with trailing slash"),
        (f"{base_url}/api", "API root"),
        (f"{base_url}/api/1.0", "API v1.0 root"),
        (f"{base_url}/swagger", "Swagger documentation"),
        (f"{base_url}/docs", "API documentation"),
        (f"{base_url}/openapi.json", "OpenAPI specification"),
        (f"{base_url}/api/1.0/swagger", "API v1.0 Swagger"),
        (f"{base_url}/api/1.0/docs", "API v1.0 docs"),
        (f"{base_url}/health", "Health check"),
        (f"{base_url}/status", "Status endpoint"),
        (f"{base_url}/version", "Version endpoint"),
        (f"{base_url}/api/1.0/arrays", "Arrays endpoint (requires auth)"),
    ]
    
    for url, description in endpoints:
        test_endpoint(session, url, description)
    
    print("\n" + "=" * 50)
    print("Summary:")
    print("- If you see 401 errors for authenticated endpoints, that's expected")
    print("- Look for any endpoints that return documentation or API specs")
    print("- Check if there are any public endpoints that provide API information")


if __name__ == "__main__":
    main()
