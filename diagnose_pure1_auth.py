#!/usr/bin/env python3
"""
Diagnostic script for Pure1 API authentication.
This script helps troubleshoot JWT authentication issues.
"""

import os
import json
import time
import requests
from datetime import datetime, timedelta, timezone
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import jwt
import base64
from dotenv import load_dotenv


def load_config():
    """Load configuration from .env file."""
    if os.path.exists('.env'):
        load_dotenv('.env')
    else:
        load_dotenv('.env.example')
    
    return {
        'app_id': os.getenv('PURESTORAGE__APP_ID'),
        'private_key_path': os.getenv('PURESTORAGE__PRIVATE_KEY_PATH'),
        'public_key_path': os.getenv('PURESTORAGE__PUBLIC_KEY_PATH'),
        'private_key_passphrase': os.getenv('PURESTORAGE__PRIVATE_KEY_PASSPHRASE'),
        'api_base_url': os.getenv('PURESTORAGE__API_BASE_URL', 'https://api.pure1.purestorage.com'),
        'proxy_url': os.getenv('PURESTORAGE__PROXY_URL')
    }


def load_keys(config):
    """Load and validate PEM keys."""
    print("Loading PEM keys...")
    
    # Load private key
    try:
        with open(config['private_key_path'], 'rb') as f:
            private_key_data = f.read()
        
        private_key = serialization.load_pem_private_key(
            private_key_data,
            password=config['private_key_passphrase'].encode() if config['private_key_passphrase'] else None
        )
        print(f"✓ Private key loaded from {config['private_key_path']}")
        print(f"  Key type: {type(private_key).__name__}")
        print(f"  Key size: {private_key.key_size} bits")
    except Exception as e:
        print(f"✗ Failed to load private key: {e}")
        return None, None
    
    # Load public key
    try:
        with open(config['public_key_path'], 'rb') as f:
            public_key_data = f.read()
        
        public_key = serialization.load_pem_public_key(public_key_data)
        print(f"✓ Public key loaded from {config['public_key_path']}")
        print(f"  Key type: {type(public_key).__name__}")
        print(f"  Key size: {public_key.key_size} bits")
    except Exception as e:
        print(f"✗ Failed to load public key: {e}")
        return private_key, None
    
    # Verify key pair matches
    try:
        # Test signing and verification
        test_data = b"test message"
        signature = private_key.sign(
            test_data,
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        
        public_key.verify(
            signature,
            test_data,
            padding.PKCS1v15(),
            hashes.SHA256()
        )
        print("✓ Private and public keys match")
    except Exception as e:
        print(f"✗ Key pair verification failed: {e}")
    
    return private_key, public_key


def generate_jwt_variants(app_id, private_key):
    """Generate different JWT token variants to test."""
    now = datetime.now(timezone.utc)
    expiry = now + timedelta(days=30)
    
    variants = []
    
    # Variant 1: Basic payload
    payload1 = {
        'iss': app_id,
        'aud': 'pure1',
        'iat': int(now.timestamp()),
        'exp': int(expiry.timestamp())
    }
    
    # Variant 2: With subject
    payload2 = {
        'iss': app_id,
        'sub': app_id,
        'aud': 'pure1',
        'iat': int(now.timestamp()),
        'exp': int(expiry.timestamp())
    }
    
    # Variant 3: With jti (JWT ID)
    payload3 = {
        'iss': app_id,
        'aud': 'pure1',
        'iat': int(now.timestamp()),
        'exp': int(expiry.timestamp()),
        'jti': f"{app_id}-{int(now.timestamp())}"
    }
    
    # Variant 4: Different audience
    payload4 = {
        'iss': app_id,
        'aud': 'https://api.pure1.purestorage.com',
        'iat': int(now.timestamp()),
        'exp': int(expiry.timestamp())
    }
    
    payloads = [
        ("Basic", payload1),
        ("With Subject", payload2),
        ("With JWT ID", payload3),
        ("Different Audience", payload4)
    ]
    
    for name, payload in payloads:
        try:
            token = jwt.encode(payload, private_key, algorithm='RS256')
            variants.append((name, token, payload))
            print(f"✓ Generated JWT variant: {name}")
        except Exception as e:
            print(f"✗ Failed to generate JWT variant {name}: {e}")
    
    return variants


def test_jwt_token(token, payload):
    """Test JWT token by decoding it."""
    try:
        # Decode without verification to check structure
        decoded = jwt.decode(token, options={"verify_signature": False})
        print(f"  Token payload: {json.dumps(decoded, indent=2)}")
        
        # Check expiry
        exp = decoded.get('exp')
        if exp:
            exp_time = datetime.fromtimestamp(exp, timezone.utc)
            print(f"  Token expires: {exp_time}")
            
            if exp_time < datetime.now(timezone.utc):
                print("  ⚠ Token is expired!")
            else:
                print("  ✓ Token is valid (not expired)")
        
        return True
    except Exception as e:
        print(f"  ✗ Token decode failed: {e}")
        return False


def test_api_call(token, config):
    """Test API call with JWT token."""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    
    # Configure session with proxy if needed
    session = requests.Session()
    if config['proxy_url']:
        session.proxies = {
            'http': config['proxy_url'],
            'https': config['proxy_url']
        }
    
    url = f"{config['api_base_url']}/api/1.0/arrays"
    
    try:
        response = session.get(url, headers=headers, timeout=30)
        print(f"  Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("  ✓ API call successful!")
            try:
                data = response.json()
                print(f"  Response: {json.dumps(data, indent=2)}")
            except:
                print(f"  Response: {response.text}")
        else:
            print(f"  ✗ API call failed")
            print(f"  Response: {response.text}")
            
            # Check response headers for additional info
            if 'www-authenticate' in response.headers:
                print(f"  WWW-Authenticate: {response.headers['www-authenticate']}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"  ✗ Request failed: {e}")
        return False


def main():
    """Main diagnostic function."""
    print("Pure1 API Authentication Diagnostics")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    print(f"Configuration:")
    print(f"  App ID: {config['app_id']}")
    print(f"  Private Key Path: {config['private_key_path']}")
    print(f"  Public Key Path: {config['public_key_path']}")
    print(f"  API Base URL: {config['api_base_url']}")
    print(f"  Proxy URL: {config['proxy_url'] or 'None'}")
    print()
    
    # Validate configuration
    if not config['app_id']:
        print("✗ App ID not configured")
        return
    
    if not config['private_key_path'] or not os.path.exists(config['private_key_path']):
        print("✗ Private key file not found")
        return
    
    # Load keys
    private_key, public_key = load_keys(config)
    if not private_key:
        return
    
    print()
    
    # Generate JWT variants
    print("Generating JWT token variants...")
    variants = generate_jwt_variants(config['app_id'], private_key)
    print()
    
    # Test each variant
    for name, token, payload in variants:
        print(f"Testing JWT variant: {name}")
        print(f"  Token (first 50 chars): {token[:50]}...")
        
        # Test token structure
        if test_jwt_token(token, payload):
            # Test API call
            print("  Testing API call...")
            test_api_call(token, config)
        
        print()


if __name__ == "__main__":
    main()
