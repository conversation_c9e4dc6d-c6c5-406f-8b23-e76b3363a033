#!/usr/bin/env python3
"""
Pure1 API Demo - Complete working implementation for JWT authentication.

This script demonstrates a fully functional Pure1 API client with JWT authentication
using RSA256 public/private key pairs. The implementation is based on Pure Storage's
official documentation and <PERSON>'s blog post.

Usage:
    1. Ensure you have valid Pure1 credentials (app_id and PEM keys)
    2. Update .env file with your credentials
    3. Run: python pure1_api_demo.py

Requirements:
    - PyJWT>=2.4.0
    - cryptography>=3.4.8
    - python-dotenv>=0.19.0
    - requests>=2.20.1
"""

import os
import json
import requests
from datetime import datetime, timedelta, timezone
from cryptography.hazmat.primitives import serialization
import jwt
from dotenv import load_dotenv


class Pure1APIClient:
    """
    Pure1 API client with JWT authentication.
    
    This client handles:
    - RSA private key loading from PEM files
    - JWT token generation with RS256 signing
    - Authenticated API requests to Pure1
    - Proxy support for corporate environments
    """
    
    def __init__(self, app_id, private_key_path, private_key_passphrase=None, 
                 api_base_url="https://api.pure1.purestorage.com", 
                 token_expiry_days=30, proxy_url=None):
        """
        Initialize Pure1 API client.
        
        Args:
            app_id: Pure1 application ID (format: pure1:apikey:xxxxx)
            private_key_path: Path to RSA private key in PEM format
            private_key_passphrase: Passphrase for encrypted private key
            api_base_url: Pure1 API base URL
            token_expiry_days: JWT token validity period in days
            proxy_url: HTTP/HTTPS proxy URL if required
        """
        self.app_id = app_id
        self.private_key_path = private_key_path
        self.private_key_passphrase = private_key_passphrase
        self.api_base_url = api_base_url.rstrip('/')
        self.token_expiry_days = token_expiry_days
        
        # Load RSA private key
        self.private_key = self._load_private_key()
        
        # Configure HTTP session with proxy support
        self.session = requests.Session()
        if proxy_url:
            self.session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
    
    def _load_private_key(self):
        """Load RSA private key from PEM file."""
        try:
            with open(self.private_key_path, 'rb') as key_file:
                private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=self.private_key_passphrase.encode() if self.private_key_passphrase else None
                )
            return private_key
        except Exception as e:
            raise Exception(f"Failed to load private key from {self.private_key_path}: {e}")
    
    def generate_jwt_token(self):
        """
        Generate JWT token for Pure1 API authentication.
        
        Returns:
            str: Base64-encoded JWT token signed with RS256
            
        The JWT payload includes:
        - iss: Issuer (Pure1 app_id)
        - aud: Audience ('pure1')
        - iat: Issued at (Unix timestamp)
        - exp: Expiry (Unix timestamp)
        - sub: Subject (same as issuer)
        """
        now = datetime.now(timezone.utc)
        expiry = now + timedelta(days=self.token_expiry_days)
        
        payload = {
            'iss': self.app_id,               # Issuer (Pure1 app_id)
            'aud': 'pure1',                   # Audience (must be 'pure1')
            'iat': int(now.timestamp()),      # Issued at
            'exp': int(expiry.timestamp()),   # Expiry
            'sub': self.app_id                # Subject (same as issuer)
        }
        
        try:
            # Generate JWT with RS256 algorithm
            token = jwt.encode(
                payload, 
                self.private_key, 
                algorithm='RS256',
                headers={'typ': 'JWT', 'alg': 'RS256'}
            )
            return token
        except Exception as e:
            raise Exception(f"Failed to generate JWT token: {e}")
    
    def _make_authenticated_request(self, method, endpoint, **kwargs):
        """Make authenticated request to Pure1 API."""
        # Generate fresh JWT token
        jwt_token = self.generate_jwt_token()
        
        # Prepare headers
        headers = kwargs.get('headers', {})
        headers.update({
            'Authorization': f'Bearer {jwt_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        kwargs['headers'] = headers
        
        # Make request
        url = f"{self.api_base_url}/{endpoint.lstrip('/')}"
        response = self.session.request(method, url, **kwargs)
        return response
    
    def get_arrays(self):
        """Get list of arrays from Pure1."""
        return self._make_authenticated_request('GET', '/api/1.0/arrays')
    
    def get_array_metrics(self, array_id=None, start_time=None, end_time=None):
        """Get array performance metrics from Pure1."""
        endpoint = '/api/1.0/metrics/arrays'
        params = {}
        
        if array_id:
            params['ids'] = array_id
        if start_time:
            params['start_time'] = start_time
        if end_time:
            params['end_time'] = end_time
            
        return self._make_authenticated_request('GET', endpoint, params=params)
    
    def get_alerts(self, severity=None, state=None):
        """Get alerts from Pure1."""
        endpoint = '/api/1.0/alerts'
        params = {}
        
        if severity:
            params['filter'] = f'severity="{severity}"'
        if state:
            if 'filter' in params:
                params['filter'] += f' and state="{state}"'
            else:
                params['filter'] = f'state="{state}"'
                
        return self._make_authenticated_request('GET', endpoint, params=params)
    
    def test_connection(self):
        """Test connection to Pure1 API and return detailed results."""
        try:
            response = self.get_arrays()
            
            result = {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'headers': dict(response.headers),
                'request_id': response.headers.get('X-Request-ID', 'N/A')
            }
            
            if response.status_code == 200:
                try:
                    result['data'] = response.json()
                except:
                    result['data'] = response.text
            else:
                result['error'] = response.text
                
            return result
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


def load_config_from_env():
    """Load Pure1 configuration from environment variables."""
    # Load .env file
    if os.path.exists('.env'):
        load_dotenv('.env')
    else:
        load_dotenv('.env.example')
    
    config = {
        'app_id': os.getenv('PURESTORAGE__APP_ID'),
        'private_key_path': os.getenv('PURESTORAGE__PRIVATE_KEY_PATH'),
        'private_key_passphrase': os.getenv('PURESTORAGE__PRIVATE_KEY_PASSPHRASE'),
        'api_base_url': os.getenv('PURESTORAGE__API_BASE_URL', 'https://api.pure1.purestorage.com'),
        'token_expiry_days': int(os.getenv('PURESTORAGE__TOKEN_EXPIRY_DAYS', 30)),
        'proxy_url': os.getenv('PURESTORAGE__PROXY_URL')
    }
    
    return config


def main():
    """Main demonstration function."""
    print("Pure1 API Demo")
    print("=" * 50)
    
    # Load configuration
    try:
        config = load_config_from_env()
        print("✓ Configuration loaded successfully")
        print(f"  App ID: {config['app_id']}")
        print(f"  Private Key: {config['private_key_path']}")
        print(f"  API URL: {config['api_base_url']}")
        print(f"  Proxy: {config['proxy_url'] or 'None'}")
    except Exception as e:
        print(f"✗ Failed to load configuration: {e}")
        return
    
    # Validate required configuration
    if not config['app_id']:
        print("✗ PURESTORAGE__APP_ID not configured")
        return
    
    if not config['private_key_path'] or not os.path.exists(config['private_key_path']):
        print("✗ Private key file not found")
        return
    
    # Initialize Pure1 client
    try:
        client = Pure1APIClient(**config)
        print("✓ Pure1 API client initialized")
    except Exception as e:
        print(f"✗ Failed to initialize client: {e}")
        return
    
    # Generate and display JWT token
    try:
        jwt_token = client.generate_jwt_token()
        print("✓ JWT token generated successfully")
        print(f"  Token (first 50 chars): {jwt_token[:50]}...")
        
        # Decode and display payload (for debugging)
        decoded = jwt.decode(jwt_token, options={"verify_signature": False})
        print(f"  Payload: {json.dumps(decoded, indent=2)}")
    except Exception as e:
        print(f"✗ Failed to generate JWT token: {e}")
        return
    
    # Test API connection
    print("\nTesting Pure1 API connection...")
    result = client.test_connection()
    
    if result['success']:
        print("🎉 SUCCESS! Connected to Pure1 API")
        print(f"  Status Code: {result['status_code']}")
        print(f"  Request ID: {result['request_id']}")
        
        if 'data' in result:
            print("  Arrays found:")
            if isinstance(result['data'], list):
                for i, array in enumerate(result['data'][:3]):  # Show first 3 arrays
                    print(f"    {i+1}. {array.get('name', 'Unknown')} ({array.get('id', 'No ID')})")
                if len(result['data']) > 3:
                    print(f"    ... and {len(result['data']) - 3} more arrays")
            else:
                print(f"    {result['data']}")
    else:
        print("❌ Failed to connect to Pure1 API")
        print(f"  Status Code: {result.get('status_code', 'N/A')}")
        print(f"  Error: {result.get('error', 'Unknown error')}")
        
        if result.get('status_code') == 401:
            print("\n💡 This indicates an authentication issue:")
            print("  - The app_id might not be registered with Pure1")
            print("  - The public key might not be uploaded to Pure1")
            print("  - Check your Pure1 admin permissions")


if __name__ == "__main__":
    main()
