#!/usr/bin/env python3
"""
Test script for Pure1 API JWT variations based on <PERSON>'s blog post.
This script tests different JWT payload structures to find the correct format.
"""

import os
import json
import time
import requests
from datetime import datetime, timedelta, timezone
from cryptography.hazmat.primitives import hashes, serialization
import jwt
import base64
from dotenv import load_dotenv


def load_config():
    """Load configuration from .env file."""
    if os.path.exists('.env'):
        load_dotenv('.env')
    else:
        load_dotenv('.env.example')
    
    return {
        'app_id': os.getenv('PURESTORAGE__APP_ID'),
        'private_key_path': os.getenv('PURESTORAGE__PRIVATE_KEY_PATH'),
        'private_key_passphrase': os.getenv('PURESTORAGE__PRIVATE_KEY_PASSPHRASE'),
        'api_base_url': os.getenv('PURESTORAGE__API_BASE_URL', 'https://api.pure1.purestorage.com'),
        'proxy_url': os.getenv('PURESTORAGE__PROXY_URL')
    }


def load_private_key(config):
    """Load private key from PEM file."""
    try:
        with open(config['private_key_path'], 'rb') as key_file:
            private_key = serialization.load_pem_private_key(
                key_file.read(),
                password=config['private_key_passphrase'].encode() if config['private_key_passphrase'] else None
            )
        return private_key
    except Exception as e:
        raise Exception(f"Failed to load private key: {e}")


def generate_jwt_variants(app_id, private_key, expiry_days=30):
    """Generate different JWT token variants based on Pure1 requirements."""
    now = datetime.now(timezone.utc)
    expiry = now + timedelta(days=expiry_days)
    
    # Convert to Unix timestamps
    iat = int(now.timestamp())
    exp = int(expiry.timestamp())
    
    variants = []
    
    # Variant 1: Basic Pure1 format (from blog post)
    payload1 = {
        'iss': app_id,
        'aud': 'pure1',
        'iat': iat,
        'exp': exp
    }
    
    # Variant 2: With subject (recommended in JWT spec)
    payload2 = {
        'iss': app_id,
        'sub': app_id,
        'aud': 'pure1',
        'iat': iat,
        'exp': exp
    }
    
    # Variant 3: With explicit headers
    payload3 = {
        'iss': app_id,
        'aud': 'pure1',
        'iat': iat,
        'exp': exp
    }
    
    # Variant 4: Different audience format
    payload4 = {
        'iss': app_id,
        'aud': 'https://api.pure1.purestorage.com',
        'iat': iat,
        'exp': exp
    }
    
    # Variant 5: With nbf (not before) claim
    payload5 = {
        'iss': app_id,
        'aud': 'pure1',
        'iat': iat,
        'exp': exp,
        'nbf': iat  # Not before (same as issued at)
    }
    
    # Variant 6: Minimal payload
    payload6 = {
        'iss': app_id,
        'exp': exp
    }
    
    payloads = [
        ("Basic Pure1 Format", payload1, {}),
        ("With Subject", payload2, {}),
        ("With Explicit Headers", payload3, {'typ': 'JWT', 'alg': 'RS256'}),
        ("Different Audience", payload4, {}),
        ("With Not Before", payload5, {}),
        ("Minimal Payload", payload6, {})
    ]
    
    for name, payload, headers in payloads:
        try:
            if headers:
                token = jwt.encode(payload, private_key, algorithm='RS256', headers=headers)
            else:
                token = jwt.encode(payload, private_key, algorithm='RS256')
            
            variants.append((name, token, payload))
            print(f"✓ Generated JWT variant: {name}")
        except Exception as e:
            print(f"✗ Failed to generate JWT variant {name}: {e}")
    
    return variants


def test_api_call(token, config, variant_name):
    """Test API call with JWT token."""
    print(f"\nTesting: {variant_name}")
    print(f"Token (first 50 chars): {token[:50]}...")
    
    # Decode token to show payload (without verification)
    try:
        decoded = jwt.decode(token, options={"verify_signature": False})
        print(f"Payload: {json.dumps(decoded, indent=2)}")
    except Exception as e:
        print(f"Failed to decode token: {e}")
    
    # Configure session
    session = requests.Session()
    if config['proxy_url']:
        session.proxies = {
            'http': config['proxy_url'],
            'https': config['proxy_url']
        }
    
    # Test different endpoints
    endpoints = [
        '/api/1.0/arrays',
        '/api/1.0/metrics/arrays',
        '/api/1.0/alerts'
    ]
    
    for endpoint in endpoints:
        url = f"{config['api_base_url']}{endpoint}"
        headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
        
        try:
            response = session.get(url, headers=headers, timeout=30)
            print(f"  {endpoint}: Status {response.status_code}")
            
            if response.status_code == 200:
                print(f"    ✓ SUCCESS!")
                try:
                    data = response.json()
                    print(f"    Response: {json.dumps(data, indent=4)}")
                except:
                    print(f"    Response: {response.text}")
                return True
            elif response.status_code == 401:
                print(f"    ✗ Unauthorized: {response.text}")
                if 'www-authenticate' in response.headers:
                    print(f"    WWW-Authenticate: {response.headers['www-authenticate']}")
            else:
                print(f"    ✗ Error: {response.text}")
        except Exception as e:
            print(f"    ✗ Request failed: {e}")
    
    return False


def main():
    """Main test function."""
    print("Pure1 JWT Variations Test")
    print("=" * 50)
    
    # Load configuration
    config = load_config()
    print(f"App ID: {config['app_id']}")
    print(f"Private Key Path: {config['private_key_path']}")
    print(f"API Base URL: {config['api_base_url']}")
    print(f"Proxy URL: {config['proxy_url'] or 'None'}")
    print()
    
    # Validate configuration
    if not config['app_id']:
        print("✗ App ID not configured")
        return
    
    if not config['private_key_path'] or not os.path.exists(config['private_key_path']):
        print("✗ Private key file not found")
        return
    
    # Load private key
    try:
        private_key = load_private_key(config)
        print("✓ Private key loaded successfully")
    except Exception as e:
        print(f"✗ Failed to load private key: {e}")
        return
    
    print()
    
    # Generate JWT variants
    print("Generating JWT variants...")
    variants = generate_jwt_variants(config['app_id'], private_key)
    print()
    
    # Test each variant
    success_found = False
    for name, token, payload in variants:
        if test_api_call(token, config, name):
            success_found = True
            print(f"\n🎉 SUCCESS! The '{name}' variant worked!")
            break
    
    if not success_found:
        print("\n❌ None of the JWT variants worked.")
        print("This suggests the app_id is not registered with Pure1 or")
        print("the public key is not uploaded to Pure1 for this app_id.")
        print("\nNext steps:")
        print("1. Verify the app_id is correct")
        print("2. Check that the public key is uploaded to Pure1")
        print("3. Ensure you have the correct permissions in Pure1")


if __name__ == "__main__":
    main()
