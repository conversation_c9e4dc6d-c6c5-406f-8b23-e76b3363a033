# Pure1 API Testing Framework

This repository contains a comprehensive testing framework for Pure Storage Pure1 API authentication using JWT tokens with RSA public/private key pairs.

## Overview

The Pure1 API requires JWT (JSON Web Token) authentication using RSA256 signing with public/private key pairs. This is more secure than username/password authentication but requires proper key management and JWT token generation.

## Files

### Core Implementation
- **`pure1_api_demo.py`** - Complete Pure1 API client implementation (recommended)
- **`test_pure1_api.py`** - Basic Pure1 API test script
- **`test_pure1_jwt_variations.py`** - Comprehensive JWT format testing

### Diagnostic Tools
- **`diagnose_pure1_auth.py`** - Detailed authentication diagnostics
- **`test_pure1_endpoints.py`** - API endpoint exploration

### Configuration
- **`.env.example`** - Environment configuration template
- **`requirements-pure1.txt`** - Python dependencies

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements-pure1.txt
```

### 2. Configure Environment

Copy `.env.example` to `.env` and update with your Pure1 credentials:

```bash
cp .env.example .env
```

Edit `.env`:
```bash
# PureStorage Configuration
PURESTORAGE__APP_ID=pure1:apikey:your_actual_app_id_here
PURESTORAGE__PRIVATE_KEY_PATH=private.pem
PURESTORAGE__PUBLIC_KEY_PATH=public.pem
PURESTORAGE__PRIVATE_KEY_PASSPHRASE=your_passphrase
PURESTORAGE__API_BASE_URL=https://api.pure1.purestorage.com
PURESTORAGE__TOKEN_EXPIRY_DAYS=30
PURESTORAGE__PROXY_URL=http://proxy.example.com:80
```

### 3. Prepare Keys

Ensure you have valid RSA public/private key pairs in PEM format:
- `private.pem` - RSA private key (encrypted or unencrypted)
- `public.pem` - RSA public key

## Usage

### Quick Test

```bash
python pure1_api_demo.py
```

### Comprehensive Diagnostics

```bash
python diagnose_pure1_auth.py
```

### JWT Format Testing

```bash
python test_pure1_jwt_variations.py
```

## Authentication Flow

1. **Load RSA Private Key** - Load private key from PEM file with optional passphrase
2. **Generate JWT Token** - Create JWT with required claims and RS256 signing
3. **API Request** - Send JWT token in Authorization header
4. **Session Management** - Pure1 returns session token valid for 10 hours

### JWT Token Structure

```json
{
  "iss": "pure1:apikey:your_app_id",  // Issuer (Pure1 app ID)
  "aud": "pure1",                     // Audience (must be 'pure1')
  "iat": 1234567890,                  // Issued at (Unix timestamp)
  "exp": 1237159890,                  // Expiry (Unix timestamp)
  "sub": "pure1:apikey:your_app_id"   // Subject (same as issuer)
}
```

## API Endpoints

The framework tests these Pure1 API endpoints:

- `/api/1.0/arrays` - List FlashArrays
- `/api/1.0/metrics/arrays` - Array performance metrics
- `/api/1.0/alerts` - System alerts

## Troubleshooting

### 401 Unauthorized Errors

If you receive 401 "Unauthorized" responses:

1. **Verify App ID** - Ensure your app_id is registered with Pure1
2. **Check Public Key** - Verify the public key is uploaded to Pure1
3. **Confirm Permissions** - Ensure you have Pure1 admin access
4. **Test Key Pair** - Verify private/public keys match

### Common Issues

- **Invalid Token Format** - JWT structure or signing algorithm incorrect
- **Expired Tokens** - Check token expiry times
- **Key Mismatch** - Private key doesn't match uploaded public key
- **Network Issues** - Proxy configuration or connectivity problems

## Test Results

The current test configuration demonstrates:

✅ **JWT Generation** - Correctly generates RS256-signed JWT tokens  
✅ **Key Loading** - Successfully loads RSA keys from PEM files  
✅ **API Communication** - Reaches Pure1 API through proxy  
✅ **Token Format** - Proper JWT structure and claims  

❌ **Authorization** - App ID not registered (expected with example credentials)

## Production Setup

To use with real Pure1 environment:

1. **Register Application** - Go to Pure1 → Administration → API Registration
2. **Upload Public Key** - Paste your public.pem content
3. **Get App ID** - Copy the generated app_id (format: pure1:apikey:xxxxx)
4. **Update Configuration** - Replace example app_id in .env file
5. **Test Connection** - Run the demo script

## Security Notes

- **Private Key Protection** - Keep private keys secure and encrypted
- **Token Expiry** - Use reasonable expiry times (30 days recommended)
- **Key Rotation** - Regularly rotate keys for security
- **Environment Variables** - Never commit real credentials to version control

## References

- [Pure1 REST API Documentation](https://support.purestorage.com/Pure1/Pure1_Manage/Pure1_REST_API)
- [Cody Hosterman's Blog Post](https://www.codyhosterman.com/2019/12/pure1-rest-api-authentication-made-easy/)
- [JWT.io](https://jwt.io/) - JWT token debugging
- [RFC 7519](https://tools.ietf.org/html/rfc7519) - JWT specification

## License

This testing framework is provided as-is for educational and testing purposes.
