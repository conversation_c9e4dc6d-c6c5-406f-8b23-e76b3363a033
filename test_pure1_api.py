#!/usr/bin/env python3
"""
Test script for Pure1 API authentication using JWT tokens with PEM keys.
This script demonstrates how to authenticate with the Pure1 API using the
configuration from .env.example file.
"""

import os
import json
import time
import requests
from datetime import datetime, timedelta
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
import jwt
import base64
from dotenv import load_dotenv


class Pure1APIClient:
    """Pure1 API client with JWT authentication using PEM keys."""
    
    def __init__(self, app_id, private_key_path, private_key_passphrase=None, 
                 api_base_url="https://api.pure1.purestorage.com", 
                 token_expiry_days=30, proxy_url=None):
        """
        Initialize Pure1 API client.
        
        Args:
            app_id: Pure1 application ID (e.g., pure1:apikey:xxxxx)
            private_key_path: Path to private PEM key file
            private_key_passphrase: Passphrase for private key (if encrypted)
            api_base_url: Pure1 API base URL
            token_expiry_days: JWT token expiry in days
            proxy_url: Proxy URL if needed
        """
        self.app_id = app_id
        self.private_key_path = private_key_path
        self.private_key_passphrase = private_key_passphrase
        self.api_base_url = api_base_url.rstrip('/')
        self.token_expiry_days = token_expiry_days
        self.proxy_url = proxy_url
        
        # Load private key
        self.private_key = self._load_private_key()
        
        # Configure session
        self.session = requests.Session()
        if proxy_url:
            self.session.proxies = {
                'http': proxy_url,
                'https': proxy_url
            }
    
    def _load_private_key(self):
        """Load private key from PEM file."""
        try:
            with open(self.private_key_path, 'rb') as key_file:
                private_key = serialization.load_pem_private_key(
                    key_file.read(),
                    password=self.private_key_passphrase.encode() if self.private_key_passphrase else None
                )
            return private_key
        except Exception as e:
            raise Exception(f"Failed to load private key from {self.private_key_path}: {e}")
    
    def _generate_jwt_token(self):
        """Generate JWT token for Pure1 API authentication."""
        now = datetime.utcnow()
        expiry = now + timedelta(days=self.token_expiry_days)
        
        payload = {
            'iss': self.app_id,  # Issuer (app_id)
            'aud': 'pure1',      # Audience
            'iat': int(now.timestamp()),      # Issued at
            'exp': int(expiry.timestamp())    # Expiry
        }
        
        try:
            # Generate JWT token using RS256 algorithm
            token = jwt.encode(payload, self.private_key, algorithm='RS256')
            return token
        except Exception as e:
            raise Exception(f"Failed to generate JWT token: {e}")
    
    def _make_request(self, method, endpoint, **kwargs):
        """Make authenticated request to Pure1 API."""
        # Generate fresh JWT token for each request
        jwt_token = self._generate_jwt_token()
        
        # Prepare headers
        headers = kwargs.get('headers', {})
        headers.update({
            'Authorization': f'Bearer {jwt_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        kwargs['headers'] = headers
        
        # Make request
        url = f"{self.api_base_url}/{endpoint.lstrip('/')}"
        
        try:
            response = self.session.request(method, url, **kwargs)
            return response
        except Exception as e:
            raise Exception(f"Request failed: {e}")
    
    def get_arrays(self):
        """Get list of arrays from Pure1."""
        response = self._make_request('GET', '/api/1.0/arrays')
        return response
    
    def get_array_metrics(self, array_id=None):
        """Get array metrics from Pure1."""
        endpoint = '/api/1.0/metrics/arrays'
        if array_id:
            endpoint += f'?ids={array_id}'
        
        response = self._make_request('GET', endpoint)
        return response
    
    def get_alerts(self):
        """Get alerts from Pure1."""
        response = self._make_request('GET', '/api/1.0/alerts')
        return response
    
    def test_connection(self):
        """Test connection to Pure1 API."""
        try:
            response = self.get_arrays()
            return {
                'success': response.status_code == 200,
                'status_code': response.status_code,
                'response': response.json() if response.status_code == 200 else response.text
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }


def load_config_from_env():
    """Load configuration from .env file."""
    # Load .env file if it exists
    if os.path.exists('.env'):
        load_dotenv('.env')
    else:
        load_dotenv('.env.example')
    
    config = {
        'app_id': os.getenv('PURESTORAGE__APP_ID'),
        'private_key_path': os.getenv('PURESTORAGE__PRIVATE_KEY_PATH'),
        'private_key_passphrase': os.getenv('PURESTORAGE__PRIVATE_KEY_PASSPHRASE'),
        'api_base_url': os.getenv('PURESTORAGE__API_BASE_URL', 'https://api.pure1.purestorage.com'),
        'token_expiry_days': int(os.getenv('PURESTORAGE__TOKEN_EXPIRY_DAYS', 30)),
        'proxy_url': os.getenv('PURESTORAGE__PROXY_URL')
    }
    
    return config


def main():
    """Main test function."""
    print("Pure1 API Test Script")
    print("=" * 50)
    
    # Load configuration
    try:
        config = load_config_from_env()
        print(f"✓ Configuration loaded")
        print(f"  App ID: {config['app_id']}")
        print(f"  Private Key Path: {config['private_key_path']}")
        print(f"  API Base URL: {config['api_base_url']}")
        print(f"  Proxy URL: {config['proxy_url'] or 'None'}")
    except Exception as e:
        print(f"✗ Failed to load configuration: {e}")
        return
    
    # Validate required configuration
    if not config['app_id']:
        print("✗ PURESTORAGE__APP_ID not set in environment")
        return
    
    if not config['private_key_path']:
        print("✗ PURESTORAGE__PRIVATE_KEY_PATH not set in environment")
        return
    
    # Initialize client
    try:
        client = Pure1APIClient(**config)
        print(f"✓ Pure1 API client initialized")
    except Exception as e:
        print(f"✗ Failed to initialize client: {e}")
        return
    
    # Test connection
    print("\nTesting connection to Pure1 API...")
    result = client.test_connection()
    
    if result['success']:
        print("✓ Successfully connected to Pure1 API!")
        print(f"  Status Code: {result['status_code']}")
        
        # Pretty print the response
        if isinstance(result['response'], dict):
            print("  Response:")
            print(json.dumps(result['response'], indent=2))
        else:
            print(f"  Response: {result['response']}")
    else:
        print("✗ Failed to connect to Pure1 API")
        if 'error' in result:
            print(f"  Error: {result['error']}")
        else:
            print(f"  Status Code: {result['status_code']}")
            print(f"  Response: {result['response']}")


if __name__ == "__main__":
    main()
