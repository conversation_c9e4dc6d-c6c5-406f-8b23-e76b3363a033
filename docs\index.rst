Pure Storage FlashArray REST Client Documentation
=================================================

.. toctree::
    :hidden:

    installation
    quick_start
    api
    changelog


Overview
--------

The **Pure Storage FlashArray REST Client** is a python module that simplifies
integration with the Pure Storage FlashArray REST interface.

It wraps REST calls with simple APIs and abstracts the HTTP request and
response handling. For specifics on API arguments, consult the REST API
guide for the Purity release currently running on the target array.

This documentation should be supplemental and attempts to explain installation,
basic usage, and provides a glossary of the exposed APIs.

:doc:`installation`
  How to get the source code, and how to build or install the python package.

:doc:`quick_start`
  A quick start guide for the REST client.

:doc:`api`
  A glossary of all exposed REST client APIs.


Community
---------

To learn what other Pure Storage customers are doing with the client, to share
an implementation or contribute code, or to interact with Pure Storage,
visit the `Pure Storage Community <http://community.purestorage.com/>`_.


Changes
-------

See the :doc:`changelog` for a list of changes to the REST Client.


License
-------

This code is licensed under the simple BSD 2-Clause License.

See the LICENSE.txt file in the top level of the source tree.


Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

